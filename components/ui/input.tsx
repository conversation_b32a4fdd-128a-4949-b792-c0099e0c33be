import * as React from 'react';
import { TextField } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

export interface InputProps extends React.ComponentProps<typeof TextField.Input> {}

const Input = React.forwardRef<
  React.ElementRef<typeof TextField.Input>,
  InputProps
>(({ className, ...props }, ref) => {
  return (
    <TextField.Input
      className={cn('', className)}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = 'Input';

export { Input };
