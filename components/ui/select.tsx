'use client';

import * as React from 'react';
import { Select as RadixSelect } from '@radix-ui/themes';

const Select = RadixSelect.Root;
const SelectGroup = ({ children }: { children: React.ReactNode }) => children;
const SelectValue = RadixSelect.Value;
const SelectTrigger = RadixSelect.Trigger;
const SelectContent = RadixSelect.Content;
const SelectItem = RadixSelect.Item;
const SelectLabel = ({ children }: { children: React.ReactNode }) => children;
const SelectSeparator = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={className} {...props} />
);

// Legacy components for backward compatibility
const SelectScrollUpButton = ({ children }: { children: React.ReactNode }) => children;
const SelectScrollDownButton = ({ children }: { children: React.ReactNode }) => children;

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
};
