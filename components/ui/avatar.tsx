'use client';

import * as React from 'react';
import { Avatar as RadixAvatar } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Avatar = React.forwardRef<
  React.ElementRef<typeof RadixAvatar>,
  React.ComponentPropsWithoutRef<typeof RadixAvatar>
>(({ className, ...props }, ref) => (
  <RadixAvatar
    ref={ref}
    className={cn('', className)}
    {...props}
  />
));
Avatar.displayName = 'Avatar';

// For backward compatibility, create simple wrapper components
const AvatarImage = ({ src, alt, ...props }: { src?: string; alt?: string; [key: string]: any }) => (
  <Avatar src={src} fallback={alt || ''} {...props} />
);

const AvatarFallback = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
  <Avatar fallback={children} {...props} />
);

export { Avatar, AvatarImage, AvatarFallback };
