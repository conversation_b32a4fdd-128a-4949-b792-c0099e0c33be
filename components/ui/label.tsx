'use client';

import * as React from 'react';
import { Text } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Label = React.forwardRef<
  React.ElementRef<typeof Text>,
  React.ComponentPropsWithoutRef<typeof Text> & { htmlFor?: string }
>(({ className, htmlFor, ...props }, ref) => (
  <Text
    as="label"
    htmlFor={htmlFor}
    ref={ref}
    className={cn('text-sm font-medium leading-none', className)}
    {...props}
  />
));
Label.displayName = 'Label';

export { Label };
