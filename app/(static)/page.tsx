import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { NavbarWrapper } from "@/components/layout/navbar-wrapper";
import {
  Code,
  Share,
  Download,
  BookOpen,
  Users,
  Zap,
  ArrowRight,
  Check
} from "lucide-react";

// Force static generation for this page
export const dynamic = 'force-static';

// Generate metadata for the home page
export async function generateMetadata() {
  return {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share powerful AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates and best practices.',
    keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy, prompt rules, AI coding',
    openGraph: {
      title: 'OnlyRules - AI Prompt Management Platform',
      description: 'Create, organize, and share powerful AI prompt rules for your favorite IDEs.',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'OnlyRules - AI Prompt Management Platform',
      description: 'Create, organize, and share powerful AI prompt rules for your favorite IDEs.',
    },
  };
}

export default function HomePage() {
  const features = [
    {
      icon: Code,
      title: "Smart Code Editor",
      description: "Built-in CodeMirror editor with syntax highlighting and IDE-specific templates."
    },
    {
      icon: Share,
      title: "Easy Sharing",
      description: "Share your prompt rules with the community or keep them private for your team."
    },
    {
      icon: Download,
      title: "Multiple Formats",
      description: "Export rules in JSON, Markdown, or IDE-specific formats for easy integration."
    },
    {
      icon: BookOpen,
      title: "Comprehensive Tutorials",
      description: "Step-by-step guides for Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, and more IDEs."
    },
    {
      icon: Users,
      title: "Community Driven",
      description: "Browse and contribute to a growing library of community-created rules."
    },
    {
      icon: Zap,
      title: "OnlyRules Integration",
      description: "Seamlessly install shared rules using the OnlyRules npm package."
    }
  ];

  const ideSupport = [
    { name: "Cursor", color: "bg-blue-500" },
    { name: "Augment Code", color: "bg-green-500" },
    { name: "Windsurf", color: "bg-purple-500" },
    { name: "General", color: "bg-gray-500" }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <NavbarWrapper />
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10" />
        <div className="container relative">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <div className="space-y-4">
              <Badge variant="outline" className="px-4 py-1">
                🚀 Supercharge Your AI Coding Experience
              </Badge>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
                Manage AI Prompt Rules
                <span className="block text-primary">Like a Pro</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Create, organize, and share powerful AI prompt rules for your favorite IDEs. 
                Boost your coding productivity with community-driven templates and best practices.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/auth/signin">
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/templates">
                  Browse Templates
                </Link>
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-3 pt-8">
              <span className="text-sm text-muted-foreground">Supports:</span>
              {ideSupport.map((ide) => (
                <Badge key={ide.name} className={`${ide.color} text-white`}>
                  {ide.name}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/50">
        <div className="container">
          <div className="max-w-2xl mx-auto text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight mb-4">
              Everything You Need for AI Prompt Management
            </h2>
            <p className="text-lg text-muted-foreground">
              Powerful tools and features designed to streamline your AI coding workflow.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => (
              <Card key={feature.title} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20">
        <div className="container">
          <div className="max-w-2xl mx-auto text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight mb-4">
              Simple 3-Step Process
            </h2>
            <p className="text-lg text-muted-foreground">
              From creation to implementation in minutes.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Create or Browse",
                description: "Write custom prompt rules or explore our community template library."
              },
              {
                step: "02",
                title: "Organize & Share",
                description: "Tag your rules, set visibility, and share with your team or the community."
              },
              {
                step: "03",
                title: "Export & Install",
                description: "Download in multiple formats or install directly using OnlyRules npm package."
              }
            ].map((item) => (
              <div key={item.step} className="text-center space-y-4">
                <div className="w-16 h-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-xl font-semibold">{item.title}</h3>
                <p className="text-muted-foreground">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container">
          <div className="max-w-2xl mx-auto text-center space-y-8">
            <h2 className="text-3xl font-bold tracking-tight">
              Ready to Transform Your AI Coding Experience?
            </h2>
            <p className="text-xl opacity-90">
              Join thousands of developers who are already using OnlyRules to boost their productivity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/auth/signup">
                  Start Building Rules
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/tutorials" className="text-primary-foreground border-primary-foreground hover:bg-primary-foreground hover:text-primary">
                  View Tutorials
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
